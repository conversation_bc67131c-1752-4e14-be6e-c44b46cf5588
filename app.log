2025-05-08 13:46:54,247 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 13:46:54,248 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:46:54,251 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:46:55,550 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:46:55,553 - werkzeug - INFO -  * Debugger PIN: 672-************-05-08 13:47:38,671 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 13:47:38,671 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:47:38,673 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:47:39,880 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:47:39,883 - werkzeug - INFO -  * Debugger PIN: 672-************-05-08 13:49:28,354 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 13:49:28,355 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:49:28,356 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:49:29,518 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:49:29,521 - werkzeug - INFO -  * Debugger PIN: 672-************-05-08 13:51:52,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 13:51:52,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:53:06,433 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:53:06] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-08 13:53:53,792 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 13:53:53,792 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:54:21,385 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:54:21] "GET /api/users/health HTTP/1.1" 200 -
2025-05-08 13:54:46,386 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 13:54:46,386 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:55:33,686 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:55:33] "GET /api/students/health HTTP/1.1" 200 -
2025-05-08 13:56:21,348 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-08 13:56:21,348 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:57:04,684 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:57:04] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-08 14:26:32,269 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-08 14:26:32,269 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:14:22,110 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 17:14:22,110 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:14:42,533 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 17:14:42,533 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:16:21,418 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 17:16:21,419 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:17:12,862 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 17:17:12,862 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:17:22,959 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-08 17:17:22,959 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:17:57,253 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-08 17:17:57,253 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:18:26,329 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 17:18:26,330 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:18:52,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 17:18:52,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:19:11,090 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 17:19:11,090 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:27:58,259 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 17:27:58,259 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:29:05,972 - common.middleware - WARNING - User with role None attempted to access /api/users/register which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-08 17:29:05,972 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 17:29:05] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 403 -
2025-05-08 17:29:17,356 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-08 17:29:17,356 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 17:29:17] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:31:01,883 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 10:31:01,883 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:31:19,544 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 10:31:19,544 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:31:28,207 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 10:31:28,207 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:44:31,782 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 10:44:31,783 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:44:31] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:48:38,595 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 10:48:38,596 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:48:38] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:50:19,520 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 10:50:19,520 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:50:19] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:51:14,707 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 10:51:14,707 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:52:05,512 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/auth/healt
2025-05-12 10:52:05,512 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:52:05] "[31m[1mGET /api/auth/healt HTTP/1.1[0m" 401 -
2025-05-12 11:02:18,971 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 11:02:18,971 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 11:04:41,761 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:04:41] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 11:06:10,824 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 11:06:10,824 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:06:10] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 11:23:47,644 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 11:23:47,645 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:23:47] "[31m[1mPOST /api/users/register?token=eyJhbGciOiJIUzI1NiJ9.e30.KqGpJZaDDx_JllyRLScTgZqWJJUlR2mqXOUFozVwNUA HTTP/1.1[0m" 401 -
2025-05-12 11:35:41,421 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 11:35:41,421 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:35:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 11:37:43,642 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:37:43,646 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:37:43] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:38:18,721 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:38:18,723 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:38:18] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:38:55,115 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:38:55,116 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:38:55] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:40:20,335 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:40:20,336 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:40:20] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:40:37,576 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:40:37] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 11:42:16,295 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:42:16] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 11:42:38,529 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/auth/login
2025-05-12 11:42:38,529 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:42:38] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:43:46,738 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/health
2025-05-12 11:43:46,738 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:43:46] "[31m[1mGET /api/users/health HTTP/1.1[0m" 401 -
2025-05-12 11:51:39,289 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:51:39] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 11:52:24,516 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:52:24,517 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:52:24] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:59:56,322 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:59:56] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-12 12:00:02,539 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:00:02] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-12 12:00:43,468 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:00:43] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-12 12:03:50,443 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 12:03:50,444 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:03:50] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 12:04:04,841 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 12:04:04,842 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:04:04] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 12:39:31,104 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:39:31] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 12:40:47,276 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:40:47,276 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:40:47] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:41:38,155 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:41:38,155 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:41:38] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:46:31,574 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:46:31,575 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:46:31] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:47:12,465 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:47:12,465 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:47:12] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:47:32,525 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:47:32,525 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:47:32] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:52:35,237 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:52:35,238 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:52:35] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:52:59,322 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:52:59,322 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:52:59] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:53:09,223 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 12:53:09,224 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:53:09] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:10,319 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:10,319 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:10] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:21,572 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:21,573 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:21] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:23,039 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:23,039 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:23] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:34,935 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:34,939 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:34] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:55:03,727 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:55:03,728 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:55:03] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:56:01,427 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 12:56:01,427 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:56:01] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:56:32,103 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 12:56:32,104 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:56:32] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:56:55,375 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:56:55,375 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:56:55] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:04:19,330 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:04:19] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:05:23,340 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:05:23,340 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:05:23] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:08:37,693 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:08:37,693 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:08:37] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:09:13,289 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:09:13] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:09:52,075 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:09:52,076 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:09:52] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:11:14,547 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:11:14] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:11:41,317 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:11:41,317 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:11:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:12:17,361 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:12:17,361 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:12:17] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:16:03,043 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 13:16:03,043 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:16:03] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:19:55,235 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:19:55] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 13:20:01,495 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:20:01] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 13:20:14,655 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:20:14] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:21:06,189 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:21:06,189 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:21:06] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:21:17,308 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:21:17] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:21:58,155 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:21:58,156 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:21:58] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:26:24,782 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:24] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 13:26:30,910 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:30] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 13:26:36,214 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:36] "GET /api/students/health HTTP/1.1" 200 -
2025-05-12 13:26:41,267 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:41] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-12 13:27:26,601 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:27:26] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:27:26,629 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:27:26,629 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:27:26] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:31:31,218 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:31:31] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:31:33,300 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:31:33,300 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:31:33] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:36:54,424 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 13:36:54,424 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:07,204 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 13:37:07,204 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:16,354 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 13:37:16,354 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:23,249 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 13:37:23,249 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:47,029 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:37:47] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:37:49,099 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:37:49,099 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:37:49] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 14:49:53,601 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 14:49:53,601 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:49:59,823 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 14:49:59,823 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:50:06,932 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 14:50:06,932 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:50:12,497 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 14:50:12,498 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:50:50,282 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 14:50:50] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 14:50:52,360 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 14:50:52,361 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 14:50:52] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:10:52,376 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:10:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:10:54,455 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:10:54,455 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:10:54] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:30:17,464 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:30:17,465 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:17,691 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:30:17,691 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:17,960 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 15:30:17,960 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:18,174 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 15:30:18,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:18,305 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 15:30:18,305 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:31:15,831 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:15] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:31:17,885 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:17] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 15:31:20,151 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:20] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:31:22,187 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:31:22,188 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:22] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:42:16,048 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:42:16,048 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:42:21,503 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:42:21,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:42:28,002 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:28] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:42:30,058 - auth_service.common.middleware - WARNING - Invalid token for path: /api/auth/verify
2025-05-12 15:42:30,059 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:30] "[31m[1mGET /api/auth/verify HTTP/1.1[0m" 401 -
2025-05-12 15:42:32,327 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:32] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:42:34,376 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:42:34,377 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:34] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:44:18,850 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:44:18,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:44:24,182 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:44:24,182 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:44:31,926 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:31] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:44:33,974 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:33] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 15:44:36,245 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:36] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:44:38,282 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:44:38,283 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:38] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:45:33,515 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:45:33,516 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:45:47,070 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:47] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:45:49,118 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:49] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 15:45:51,371 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:51] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:45:53,415 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:45:53,418 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:53] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:47:41,197 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:47:41,198 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:58:04,442 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:58:04] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:58:10,802 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:58:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:59:50,255 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:59:50,255 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:00:06,491 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:06] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:00:08,540 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:08] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:00:10,808 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:00:12,856 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 16:00:12,857 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:12] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:01:20,281 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:01:20,281 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:01:35,461 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:35] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:01:37,517 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:37] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:01:39,797 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:01:41,834 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register, error: Signature verification failed
2025-05-12 16:01:41,835 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:02:39,794 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:02:39,794 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:02:46,709 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:46] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:02:48,757 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:48] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:02:51,017 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:51] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:02:53,049 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register, error: Signature verification failed
2025-05-12 16:02:53,050 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:53] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:04:10,711 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 16:04:10,712 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:04:20,424 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:04:20,424 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:04:32,700 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:32] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:04:34,749 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:34] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:04:37,029 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:04:39,330 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:39] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 16:30:42,573 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 16:30:42,574 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:31:02,602 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:31:02,602 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:31:39,914 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:39] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:31:41,969 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:41] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:31:44,263 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:31:46,538 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:46] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 16:32:37,782 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:32:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:33:38,260 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:33:38] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 16:49:45,523 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 16:49:45,523 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,523 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,751 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 16:49:45,751 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,899 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 16:49:45,899 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,971 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 16:49:45,971 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:50:05,018 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:50:05,578 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:50:05,923 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:05,925 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:05,924 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:05,925 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:32,482 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:32,482 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:32,796 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:32,797 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:58,735 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 16:50:58,735 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:58] "[31m[1mOPTIONS /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:57:57,579 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:57:57,580 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:57,580 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:57,796 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 16:57:57,796 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:57,970 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 16:57:57,970 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:58,192 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 16:57:58,192 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:58:43,151 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:43] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:58:43,693 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:43] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:58:44,024 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:58:44,025 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:58:44,026 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:58:44,026 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:59:04,178 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 16:59:04,179 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:59:04] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 16:59:39,755 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users
2025-05-12 16:59:39,755 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:59:39] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025-05-12 16:59:53,636 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:59:53] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:00:17,219 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:00:17] "[33mGET /api/users HTTP/1.1[0m" 404 -
2025-05-12 17:00:33,716 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:00:33] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 17:07:39,832 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:07:39,832 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,073 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 17:07:40,073 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,297 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:07:40,297 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,426 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:07:40,426 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,618 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:07:40,618 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:29:46,394 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:29:46,396 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:29:46,705 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:46,707 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:29:46,967 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:46,968 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:29:54,692 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:29:54,693 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:54] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 17:29:55,618 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:55] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:29:55,622 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:55,623 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:55] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:30:11,135 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:11,136 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:11] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:30:11,232 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-12 17:30:11,233 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:11] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-12 17:30:53,334 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:30:53,335 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:30:53,337 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:53,337 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:30:53,643 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:53,643 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:31:12,834 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:31:12,834 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:31:12] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 17:31:18,134 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:31:18] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:31:18,445 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:31:18,445 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:31:18] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:36:16,751 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:16] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:36:16,753 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:16] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:36:17,059 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:36:17,059 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:17] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:36:17,323 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:36:17,324 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:17] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:36:22,445 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:36:22,445 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:22] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 17:42:17,000 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:42:17,000 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,295 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 17:42:17,295 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,535 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:42:17,535 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,708 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:42:17,708 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,803 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:42:17,803 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:24,690 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:48:24,690 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:24,762 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 17:48:24,762 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:25,016 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:48:25,016 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:25,174 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:48:25,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:25,276 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:48:25,276 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:49:00,138 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:49:00] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:49:02,449 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:49:02] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:49:19,365 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:49:19] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:58:39,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:58:39,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:58:39,211 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,211 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:58:39,211 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,280 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:58:39,280 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:54,173 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:54] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:58:54,174 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:54] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:58:54,998 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:54] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:58:55,302 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:55] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:59:04,597 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:59:04] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:59:38,907 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:59:38] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:00:04,216 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:00:04] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:00:17,969 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:00:17] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:02:59,996 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:02:59,996 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,143 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:04:21,143 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,327 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:04:21,327 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,548 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:04:21,548 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,835 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:04:21,835 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,838 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:04:21,838 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:36,271 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:36] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:04:36,836 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:36] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:04:37,196 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:04:37,201 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:04:37,506 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:04:37,507 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:04:37,770 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:04:37,770 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:09:18,211 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:09:18,212 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:18,816 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:10:18,816 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,062 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:10:19,063 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,291 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:10:19,292 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,471 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:10:19,472 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,598 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:10:19,598 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:28,357 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:10:28,361 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:10:28,666 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:28,666 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:10:28,928 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:28,928 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:10:42,737 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:42] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:10:42,778 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:42,778 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:42] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:10:43,082 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:43,082 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:43] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:12:13,704 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:12:13,704 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:12:53,351 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:12:53,351 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:13:30,227 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:13:30,227 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:15:39,162 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:15:39,163 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:16:09,773 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:09,774 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:09] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:10,036 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:10,036 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:10] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:18,851 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:18,853 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:18] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:19,166 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:19,166 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:19] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:27,342 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:27,343 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:27] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:27,603 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:27,604 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:27] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:58,652 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:17:07,046 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:17:07,046 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:17:17,886 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:17:17,887 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:17:17] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:18:36,504 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:18:36,504 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:18:49,524 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:18:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:19:12,247 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:19:12,248 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:19:12] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:20:48,479 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:20:48,480 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:21:04,149 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:21:04,150 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:21:04] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:21:16,607 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:21:16] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:21:33,306 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:21:33,307 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:21:33] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:20,999 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:22:20,999 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,312 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:22:21,312 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,625 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:22:21,625 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,711 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:22:21,711 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,827 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:22:21,827 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:28,514 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:28,515 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:28,779 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:28,780 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:34,415 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:34] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:22:34,440 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:34,440 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:34] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:34,744 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:34,745 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:34] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:38,090 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:38] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-12 18:22:38,413 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:38] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-12 18:22:39,873 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:39,873 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:39] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:41,789 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-12 18:22:42,620 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:42,621 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:42] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:44,917 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:44,918 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:44] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:45,181 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:45,181 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:45] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:46,099 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:46,100 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:46,407 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:46,407 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:47,482 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:47,483 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:47] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:47,747 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:47,748 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:47] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:58,615 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:22:58,678 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:58,679 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:58] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:58,984 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:58,985 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:58] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:24:21,823 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:24:21,824 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:24:21] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:24:32,618 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:24:32] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:24:46,414 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:24:46,415 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:24:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:25:44,050 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:25:44,050 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:25:47,015 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:25:47] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:25:59,241 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:25:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:25:59,497 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:25:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:28:07,824 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:28:07,824 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,028 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:28:08,028 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,306 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:28:08,307 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,572 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:28:08,572 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,622 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:28:08,622 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:43,602 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:28:43,604 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:28:43] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:29:48,623 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:29:48,624 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:29:48] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:30:28,450 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:30:28] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:31:26,477 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-12 18:31:26,478 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:31:26] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-12 18:31:35,821 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-12 18:31:35,822 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:31:35] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-12 18:31:44,890 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:31:44] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 18:32:06,212 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:32:06,213 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:32:06] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:32:13,292 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:32:13,293 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:32:13] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:33:19,245 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:33:19] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:34:55,993 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:34:55,995 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:34:55] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:35:49,051 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:49,310 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:49,420 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:58,044 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:58,417 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:58,899 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:43:24,637 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:43:24,638 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:24,850 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:43:24,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:25,070 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:43:25,070 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:25,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:43:25,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:25,331 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:43:25,331 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:41,097 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:43:41,098 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:43:41] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:44:20,912 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:44:20,913 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:44:20] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:48:46,835 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:48:46,836 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:49:11,918 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:49:11,920 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:49:11] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:49:21,429 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:49:21,430 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:49:21] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:50:38,623 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:50:38,623 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:50:45,496 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:50:45,496 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:50:50,138 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:50:50,139 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:50:50] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:51:49,743 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:51:49,743 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:52:00,493 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:52:00,494 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:52:00] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:53:02,607 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:53:02] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:53:24,108 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:53:24,109 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:53:24] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:55:04,580 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:55:04] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 19:02:44,550 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 19:02:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:27,570 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:27] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:28,259 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:28] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:59,650 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:59] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:59,985 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 10:23:59,985 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:59] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 10:23:59,986 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 10:23:59,987 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:59] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 10:46:02,147 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 10:46:02,147 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 10:46:02,148 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 10:46:02,148 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 10:46:21,102 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:46:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:48:56,349 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:48:56] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 10:56:39,428 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 10:56:39,430 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:56:39] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 10:57:35,535 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 10:57:35,536 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:57:35] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 10:59:05,979 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:59:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:00:21,836 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:00:21] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 11:01:22,759 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:01:22] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:01:23,109 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 11:01:23,109 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:01:23] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 11:01:23,109 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 11:01:23,110 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:01:23] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 11:03:51,824 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:03:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:09:03,927 - auth_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/auth/healt
2025-05-13 11:09:03,928 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:09:03] "[31m[1mGET /api/auth/healt HTTP/1.1[0m" 401 -
2025-05-13 11:09:11,615 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:09:11] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-13 11:13:06,015 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users/{7}
2025-05-13 11:13:06,015 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:06] "[31m[1mGET /api/users/users/{7} HTTP/1.1[0m" 401 -
2025-05-13 11:13:16,696 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:13:16,696 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:16] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:13:28,118 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:13:28,118 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:28] "[31m[1mGET /api/users/users/{7} HTTP/1.1[0m" 403 -
2025-05-13 11:13:40,479 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{user_id = 7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:13:40,479 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:40] "[31m[1mGET /api/users/users/{user_id%20=%207} HTTP/1.1[0m" 403 -
2025-05-13 11:14:05,332 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:14:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:14:16,931 - common.middleware - WARNING - Invalid token for path: /api/users/users/{user_id = 7}
2025-05-13 11:14:16,932 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:14:16] "[31m[1mGET /api/users/users/{user_id%20=%207} HTTP/1.1[0m" 401 -
2025-05-13 11:15:03,340 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:15:03] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:15:27,312 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{user_id = 7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:15:27,312 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:15:27] "[31m[1mGET /api/users/users/{user_id%20=%207} HTTP/1.1[0m" 403 -
2025-05-13 11:15:37,076 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:15:37,076 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:15:37] "[31m[1mGET /api/users/users/{7} HTTP/1.1[0m" 403 -
2025-05-13 11:17:28,578 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:17:28,578 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:17:28] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:18:22,461 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:18:22,461 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:18:22] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:18:53,619 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:18:53] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:19:06,373 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:19:06,373 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:19:06] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:19:49,384 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:19:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:23:36,273 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:23:36,273 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:23:36] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:23:46,639 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/1 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:23:46,639 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:23:46] "[31m[1mGET /api/users/users/1 HTTP/1.1[0m" 403 -
2025-05-13 11:23:57,389 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/5 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:23:57,390 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:23:57] "[31m[1mGET /api/users/users/5 HTTP/1.1[0m" 403 -
2025-05-13 11:28:36,905 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:28:36,914 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:28:36] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:29:01,259 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:29:01,259 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:29:01] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:29:31,785 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:29:31,786 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:29:31] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:33:22,763 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:33:22,763 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:33:22] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:33:28,312 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:33:28,344 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:33:28] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:30,953 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:30,954 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:30] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:32,074 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:32,074 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:32] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:32,744 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:32,744 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:34:32] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:33,303 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:33,304 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:33] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:34,470 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:34,470 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:34] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:36,692 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:36,692 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:36] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:37:45,934 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:37:45,935 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:37:45] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:38:00,449 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:38:00,450 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:38:00] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:39:11,408 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:39:11,409 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:39:11] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:43:49,180 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:43:49] "GET /api/users/health HTTP/1.1" 200 -
2025-05-13 11:45:23,523 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:45:23] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:46:18,036 - student_service.common.middleware - WARNING - Invalid token for path: /register
2025-05-13 11:46:18,037 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:46:18] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:46:20,629 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:46:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:46:30,431 - common.middleware - WARNING - Invalid token for path: /api/users/users/5
2025-05-13 11:46:30,431 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:46:30] "[31m[1mGET /api/users/users/5 HTTP/1.1[0m" 401 -
2025-05-13 11:46:52,507 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:46:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:47:08,601 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:47:08] "GET /api/users/users/5 HTTP/1.1" 200 -
2025-05-13 11:47:56,113 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:47:56,113 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:47:56] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:49:39,658 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:49:39,659 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:49:39] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:50:06,205 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/students
2025-05-13 11:50:06,236 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:50:06] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 401 -
2025-05-13 11:50:15,219 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:50:15] "GET /api/users/users/5 HTTP/1.1" 200 -
2025-05-13 11:51:02,437 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:51:02] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:51:12,490 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:51:12] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-13 11:54:27,411 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:54:27,412 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:54:27] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:54:46,435 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:54:46,435 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:54:46] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 12:10:39,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:10:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 12:11:06,972 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:11:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 12:11:08,463 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:11:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 12:11:37,090 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:11:37] "GET /api/students/students/1 HTTP/1.1" 200 -
2025-05-13 13:22:18,791 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 13:22:18,794 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 13:22:18] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 14:39:21,949 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 14:39:21,950 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,950 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 14:39:21,950 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 14:39:21,950 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,950 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,953 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 14:39:21,953 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,988 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 14:39:21,988 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:29,952 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:39:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 14:40:06,098 - user_service.common.utils - ERROR - Error: You don't have permission to register a Student, Status: 403
2025-05-13 14:40:06,099 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:40:06] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 403 -
2025-05-13 14:40:29,716 - user_service.common.utils - ERROR - Error: You don't have permission to register a Teacher, Status: 403
2025-05-13 14:40:29,716 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:40:29] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 403 -
2025-05-13 14:54:04,978 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 14:54:04,978 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,212 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 14:54:05,212 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,413 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 14:54:05,413 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,640 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 14:54:05,640 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,751 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 14:54:05,751 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:48,299 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:54:48] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 14:55:20,690 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:55:20] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 14:56:24,455 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:56:24] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 14:58:37,589 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:58:37] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 14:59:03,516 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:59:03] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 15:01:58,697 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:01:58,697 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:01:58] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:01:58,697 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:01:58,698 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:01:58] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:03,182 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:03] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 15:02:03,790 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 15:02:03,792 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:03] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 15:02:15,168 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 15:02:15,231 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:15,231 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:15] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:15,533 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:15,533 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:15] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:32,194 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:32,194 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:32,195 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:32,195 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:07:40,039 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:07:40] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-13 15:13:51,636 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:13:51] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 15:15:52,809 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-13 15:15:52,810 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:15:52] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-13 15:16:27,984 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:16:27] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-13 15:18:07,343 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:18:07,347 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:18:07] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:23:19,473 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:23:19,474 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:23:19] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:31:40,464 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:31:40,465 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:31:40] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:32:52,208 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:32:52] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:32:52,209 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:32:52,210 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:32:52] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:34:45,318 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 15:34:45,318 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:45,536 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 15:34:45,536 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:45,831 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 15:34:45,832 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:46,029 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 15:34:46,029 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:46,177 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 15:34:46,177 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:35:35,646 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:35:35] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 15:35:51,485 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:35:51,485 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:35:51] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:36:29,144 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:36:29] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 15:38:42,118 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:38:42,118 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:38:42] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:45:02,805 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:45:02,805 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:45:02] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:47:43,746 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:47:43] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:47:43,746 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:47:43,748 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:47:43] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:48:29,302 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-13 15:48:29,303 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:48:29] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-13 15:48:31,362 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 15:48:31,363 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:48:31] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-13 15:48:59,863 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:48:59] "[31m[1mGET /api/students/user/13 HTTP/1.1[0m" 405 -
2025-05-13 15:49:01,915 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:49:01] "[31m[1mGET /api/parents/user/14 HTTP/1.1[0m" 405 -
2025-05-13 15:50:31,583 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:31] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:50:31,583 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:31,584 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:31] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:35,681 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:35] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:50:35,682 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:35,683 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:35] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:37,733 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:37,734 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:37] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:41,833 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:41] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-13 15:50:41,834 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:41,834 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:41] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:45,932 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:45] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-13 15:50:45,932 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:45,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:45] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:47,978 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:47,979 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:47] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:52,065 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:52] "[31m[1mGET /api/parents/3 HTTP/1.1[0m" 405 -
2025-05-13 15:50:52,066 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:52,067 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:52] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:52,940 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:50:52,941 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:52] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:50:56,172 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:56] "[31m[1mGET /api/parents/3 HTTP/1.1[0m" 405 -
2025-05-13 15:50:56,173 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:56,175 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:56] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:58,226 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:58,227 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:58] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:51:57,330 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-13 15:51:57,331 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:51:57] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-13 15:51:59,384 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 15:51:59,385 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:51:59] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-13 15:52:03,475 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:03] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:52:03,476 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:52:03,477 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:03] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:52:45,862 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:45] "[31m[1mGET /api/parents/14 HTTP/1.1[0m" 405 -
2025-05-13 15:52:45,863 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:52:45,864 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:45] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:53:25,274 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:53:25] "[31m[1mGET /api/parents/14 HTTP/1.1[0m" 405 -
2025-05-13 15:53:25,274 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:53:25,275 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:53:25] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:54:02,159 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:02] "[31m[1mGET /api/parents/14 HTTP/1.1[0m" 405 -
2025-05-13 15:54:02,160 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:54:02,161 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:02] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:54:48,392 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:48,392 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:48] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:50,431 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:50,432 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:50] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:52,470 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:52,471 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:52] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:54,520 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:54,520 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:54] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:56,564 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:56,564 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:56] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:58,610 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:58,610 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:58] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:00,667 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:00,667 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:00] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:02,715 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:02,715 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:02] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:04,763 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:04,764 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:04] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:06,816 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:06,817 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:06] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:08,863 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:08,863 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:08] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:10,910 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:10,911 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:10] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:19,998 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 15:55:19,998 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:55:27,541 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:27,542 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:27] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:29,592 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:29,593 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:29] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:31,642 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:31,642 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:31] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:33,689 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:33,690 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:33] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:35,742 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:35,742 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:35] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:37,794 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:37,794 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:37] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:39,847 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:39,847 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:39] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:41,904 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:41,905 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:41] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:43,954 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:43,955 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:43] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:46,008 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:46,009 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:46] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:48,054 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:48,054 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:48] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:50,090 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:50,091 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:50] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:57:37,760 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:57:37] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:57:37,761 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:57:37,762 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:57:37] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:58:21,993 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:58:21,993 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:58:21] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 16:00:05,691 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 16:00:05,692 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:00:05] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-13 16:02:08,495 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 16:02:08,495 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:02:08] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 16:03:28,571 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 16:03:28,571 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:28,785 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 16:03:28,785 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:29,025 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 16:03:29,026 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:29,205 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:03:29,205 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:29,339 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 16:03:29,339 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:38,502 - student_service.common.utils - ERROR - Error: Error mapping parent to student: (psycopg2.errors.ForeignKeyViolation) insert or update on table "parent_students" violates foreign key constraint "parent_students_student_id_fkey"
DETAIL:  Key (student_id)=(13) is not present in table "students".

[SQL: INSERT INTO parent_students (parent_id, student_id, relationship, created_at) VALUES (%(parent_id)s, %(student_id)s, %(relationship)s, %(created_at)s) RETURNING parent_students.id]
[parameters: {'parent_id': 14, 'student_id': 13, 'relationship': 'Mother', 'created_at': datetime.datetime(2025, 5, 13, 10, 33, 38, 492956)}]
(Background on this error at: https://sqlalche.me/e/14/gkpj), Status: 500
2025-05-13 16:03:38,502 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:03:38] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 500 -
2025-05-13 16:04:44,398 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:04:44] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-13 16:10:46,456 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:10:46,461 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:10:46] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:11:55,287 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:11:55] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:12:48,654 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:12:48,655 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:12:48] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:13:45,781 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:13:45,781 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:13:45] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:15:04,683 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:15:04,684 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:15:04] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:15:12,207 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:15:12] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:15:31,450 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:15:31,451 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:15:31] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:19:45,157 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:19:45,158 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:20:18,768 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:20:18] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-13 16:21:17,858 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:21:17] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-13 16:24:12,268 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses/1
2025-05-13 16:24:12,268 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:24:12] "[31m[1mGET /api/courses/courses/1 HTTP/1.1[0m" 401 -
2025-05-13 16:24:19,751 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:24:19] "GET /api/courses/courses/1 HTTP/1.1" 200 -
2025-05-13 16:25:41,548 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/map-student
2025-05-13 16:25:41,549 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:25:41] "[31m[1mPOST /api/courses/map-student HTTP/1.1[0m" 401 -
2025-05-13 16:25:52,473 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:25:52] "[31m[1mGET /api/students/13 HTTP/1.1[0m" 405 -
2025-05-13 16:25:52,474 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:25:52,475 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:25:52] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-13 16:26:26,557 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:26:26] "[31m[1mGET /api/students/2 HTTP/1.1[0m" 405 -
2025-05-13 16:26:26,557 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:26:26,559 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:26:26] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-13 16:28:15,384 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:28:15] "[31m[1mGET /api/students/2 HTTP/1.1[0m" 405 -
2025-05-13 16:28:15,385 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:28:15,386 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:28:15] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-13 16:32:21,747 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:32:21,747 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:32:49,497 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:32:49] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-13 16:36:43,723 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:36:43] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-13 16:40:22,932 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/student-courses/1
2025-05-13 16:40:22,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:40:22] "[31m[1mGET /api/courses/student-courses/1 HTTP/1.1[0m" 401 -
2025-05-13 16:40:29,882 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:40:29] "GET /api/courses/student-courses/1 HTTP/1.1" 200 -
2025-05-13 16:52:18,448 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 16:52:18,449 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:18,657 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 16:52:18,657 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:18,870 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 16:52:18,870 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:19,030 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:52:19,030 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:19,129 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 16:52:19,129 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:37,052 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:52:37,584 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:52:37,913 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:52:37,913 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 16:52:37,913 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:52:37,914 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 16:54:15,077 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 16:54:15,078 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:25,805 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 16:54:25,805 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:35,921 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 16:54:35,921 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:49,481 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 16:54:49,481 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:53,343 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:54:53,343 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:55:14,258 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:55:14] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 16:55:44,094 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:55:44,094 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:55:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 16:55:44,095 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:55:44,095 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:55:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:00:29,599 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:00:29,599 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:00:29] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:00:29,602 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:00:29,603 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:00:29] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:01:28,288 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:01:28,289 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:01:28] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:01:28,599 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:01:28,600 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:01:28] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:02:49,099 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:02:49,100 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:02:49] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:02:49,101 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:02:49,101 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:02:49] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:05:43,809 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:05:43,810 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:05:43] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:05:43,810 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:05:43,810 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:05:43] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:06:08,388 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:06:08] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 17:06:08,782 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-13 17:06:08,784 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:06:08] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-13 17:10:57,339 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:10:57,340 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:10:57] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:10:57,426 - common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-13 17:10:57,427 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:10:57] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-13 17:11:23,765 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:11:23,765 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:23] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:11:24,070 - common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-13 17:11:24,071 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:24] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-13 17:11:36,753 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:36] "[31m[1mGET /api/users/register HTTP/1.1[0m" 405 -
2025-05-13 17:11:36,839 - common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-13 17:11:36,839 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:36] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-13 17:17:06,276 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:06,277 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:06] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:06,278 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:06,279 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:06] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:08,574 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:08] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:08,892 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:08] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:21,062 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:21,062 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:21] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:24,424 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:24] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:24,431 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:40,470 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:40,470 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:40] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:41,982 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:41] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:42,300 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:42] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:19:58,634 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:19:58,634 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:19:58] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:24:16,832 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 17:24:16,832 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:16,893 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 17:24:16,893 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:17,150 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 17:24:17,150 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:17,303 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 17:24:17,303 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:17,359 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 17:24:17,359 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:20,766 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:24:20,766 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:24:20] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:24:20,766 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:24:20,767 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:24:20] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:25:20,874 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:25:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 17:27:20,075 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:27:20] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 17:27:35,406 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:27:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:27:35,737 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:27:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:28:36,402 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:28:36] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:28:36,762 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:28:36] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-13 17:28:36,982 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:28:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:36:26,683 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:36:26,684 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:36:26] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:36:26,685 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:36:26,685 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:36:26] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:36:38,449 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:36:38] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 17:42:23,705 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 17:42:23,705 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:42:57,010 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:42:57] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 17:42:59,341 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:42:59] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 17:43:11,988 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:43:11,988 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:43:11] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:43:11,989 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:43:11,989 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:43:11] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:45:02,528 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 17:45:02,529 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:02,728 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 17:45:02,729 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:02,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 17:45:02,937 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:03,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 17:45:03,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:03,259 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 17:45:03,259 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:08,045 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 17:45:08,046 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 17:45:08,390 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 17:45:08,691 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:09,867 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:09] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:10,199 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:11,433 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:12,032 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:14,891 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:14] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:09:15,266 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:15] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:09:18,106 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:18] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:10:40,371 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:40] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:10:40,686 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:10:41,453 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:41] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:10:43,449 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:43] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:10:50,046 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:10,089 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:10,366 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:12,818 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:13,174 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:13] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:14,326 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:14] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:14,583 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:14] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:28,013 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:28] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:28,329 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:28] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:46,108 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:11:49,822 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:49] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:49,826 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:49] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:52,281 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:52] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:00,724 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:00] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:12:01,050 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:12:01,052 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:01] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:12:10,357 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:12:12,016 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:12] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:16,565 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:16] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:31,417 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:31] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:12:31,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:12:36,274 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:36] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:41,115 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:46,888 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:46,889 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:07,032 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:07] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:13:07,560 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:07] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:27,344 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:36,491 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:36] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:13:36,806 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:13:37,920 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:37,923 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:03,419 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:04,808 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:25,128 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:28,457 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:28] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:39,584 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:39] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:15:33,007 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 18:15:33,008 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,206 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 18:15:33,206 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,407 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:15:33,407 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,593 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 18:15:33,593 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,720 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 18:15:33,720 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:37,066 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:15:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:15:37,429 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:15:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:15:37,735 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:15:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:20:26,516 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:20:26,519 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:20:26] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:20:38,449 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:20:38,450 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:20:38] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:21:11,873 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:11] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:21:12,219 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:12,221 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:12,527 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,531 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:12,790 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,791 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:12,838 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,839 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:29,465 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:29,467 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:29,468 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:29] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:29,777 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:30,082 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:30,082 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:30] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:31,226 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,227 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:31] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:31,532 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,533 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:31] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:31,843 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,843 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:31] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:21,157 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:21,159 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:21,467 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,467 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:21,733 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:21,779 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,779 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:29,222 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:29,230 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:29,230 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:29,531 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:29,843 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:29,843 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:44,559 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:32:44,974 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:44] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:32:45,235 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:45] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:33:13,286 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:33:13,287 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:33:34,601 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:34] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 18:33:35,307 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:35] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 18:33:35,367 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:35] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:33:57,702 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:57] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:33:58,030 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:33:58,031 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:33:58,337 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:33:58,338 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:33:58,602 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:33:58,602 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:34:35,750 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:34:35,750 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:34:35] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:34:37,799 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:34:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:34:54,503 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:34:54,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:35:00,823 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:00,823 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:00] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:02,875 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:35:17,789 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:17] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:35:17,790 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:17] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:35:18,098 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:18,099 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:18] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:18,362 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:18,363 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:18] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:39,159 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:35:39,159 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:35:46,017 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:46,017 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:46] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:48,069 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:48] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:36:19,731 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:36:19,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:36:19] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:36:32,691 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:36:32,692 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:36:32] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:36:37,875 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:36:37,876 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:36:37] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:37:04,307 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:37:04,307 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:37:16,726 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:16] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:37:17,153 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:17] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:17,420 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:17] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:26,165 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:26] "[31m[1mGET /api/students/student-profile HTTP/1.1[0m" 405 -
2025-05-13 18:37:46,256 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:37:46,256 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:37:49,667 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:50,035 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:51,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:53,433 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:53] "[31m[1mGET /api/students/student-profile HTTP/1.1[0m" 405 -
2025-05-13 18:37:56,739 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:56] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:58,023 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:59,174 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:39:19,708 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:39:19] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:39:20,422 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:39:20,422 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:39:51,231 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:39:51] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:39:56,545 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:39:56] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:40:10,075 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:40:29,995 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:29] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:40:39,135 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:40:40,435 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:40:40,441 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,428 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,430 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,431 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,432 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:10,765 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:15,621 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:42:26,580 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 18:42:26,580 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:26,836 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 18:42:26,836 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:27,072 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:42:27,072 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:27,356 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 18:42:27,356 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:27,366 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 18:42:27,367 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:32,270 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:32] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:42:32,620 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:32] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:42:32,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:32] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:42:35,224 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:42:35,546 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:42:38,743 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:43:56,742 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:43:56] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 18:43:56,794 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:43:56] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:44:04,955 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:04] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:44:05,286 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:05,287 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:05,547 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:05,610 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:05,872 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:06,127 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:06,187 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:06,432 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:10,904 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:10] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:10,921 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:10] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:11,211 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:11,233 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:11,487 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:11,548 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:12,684 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:12] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:12,685 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:12] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:12,691 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:13,009 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:13,323 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:13,630 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,021 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:15,337 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:15,342 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,599 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,663 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,911 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:49,746 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:44:49,748 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:49] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:44:55,537 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:44:55,538 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:55] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:45:05,437 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:45:05,500 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:05,817 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:18,307 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:18] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:45:18,310 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:18,616 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:45:18,619 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:18] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:45:20,679 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:20] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:21,001 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:21,595 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:21,598 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:24,044 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:49,765 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:45:50,093 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 18:45:50,094 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 18:45:50,455 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:45:50,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:46:20,953 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:20] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 18:46:21,320 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:21] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:46:37,841 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:46:38,177 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:46:38,442 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:46:57,329 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:46:57,643 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:57] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:46:57,952 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:46:57,952 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:57] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:00,745 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:01,635 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:01,635 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:01] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:01,638 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:03,230 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:03] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:47:03,540 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:03] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:47:04,561 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:47:04,879 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:06,447 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:11,087 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:11] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:47:11,393 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:11,393 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:11] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:11,394 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:17,422 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:20,492 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:20] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:47:20,494 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:20,495 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:20,496 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:20] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:22,094 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:48:07,671 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:07] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:48:08,053 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:48:08,307 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:48:28,330 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:28] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:48:28,643 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:28] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:48:48,786 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:48] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:49:09,782 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:49:09] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:49:09,789 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:49:09] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:49:13,010 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:49:13] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:04:41,431 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 19:04:41,432 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,432 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,445 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 19:04:41,445 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,450 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 19:04:41,450 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,510 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 19:04:41,511 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:05:00,489 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:00] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:05:01,164 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:05:01,535 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:01,536 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:01,856 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:02,175 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:02] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:16,759 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:16] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:05:17,081 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:17] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:05:30,231 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:05:30,556 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:30,557 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:30,877 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:31,130 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:35,583 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:35,882 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:05:36,191 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:05:36,195 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:36] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:05:43,137 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:43] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:43,458 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:58,350 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:58] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:58,355 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:58,660 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:06:31,939 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:06:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:06:33,271 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:06:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:06:33,531 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:06:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:38,914 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:39,167 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:42,076 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:42,388 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:42] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:08:42,703 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:08:43,411 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:08:47,842 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:47] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:08:47,844 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:48,153 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:08:48,153 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:48] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:08:56,642 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:02,658 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:02] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:09:02,662 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:09:02,662 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:02] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:09:02,976 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:21,465 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:22,327 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:22,641 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:22] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:09:22,952 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:09:22,952 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:22] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:10:28,920 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:10:37,670 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:10:38,050 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:10:38,321 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:10:39,162 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:39] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:10:39,477 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:39] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:17:53,491 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,491 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,501 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 10:17:53,501 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:18:02,449 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:02] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 10:18:02,451 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:02] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 10:18:02,775 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:02] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:18:03,076 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:03] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:23,033 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 10:35:23,033 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:35:23,258 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 10:35:23,258 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:35:23,470 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 10:35:23,471 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:35:23,607 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 10:35:23,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:35:23,698 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 10:35:23,698 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:35:37,410 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:37,412 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:37,727 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:38,037 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:41,536 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:41] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:35:41,870 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:35:42,521 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:42] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:42,849 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:43,840 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:44,143 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:44] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:35:44,474 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:44] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:35:46,769 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:46] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:48,996 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:48] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:35:49,003 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:49] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:35:50,149 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:50,600 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:50] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:35:50,608 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:50,608 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:50] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:35:52,662 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:52] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:35:55,369 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:55] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:35:55,686 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:55] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:35:57,357 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:58,005 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:35:58,304 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:58] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:35:58,619 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:35:59,771 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:35:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:00,832 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:00] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:36:01,252 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:01] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:36:01,261 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:36:22,003 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:22,860 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:22] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:36:22,865 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:22] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:36:23,168 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:26,650 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:26] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:36:28,469 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:31,261 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:31] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:36:31,269 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:31,576 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:36:32,447 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:32] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:36:48,421 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:48] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:36:48,735 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:48] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:36:49,072 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:49,901 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:49] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:36:49,907 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:49] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:36:50,218 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:36:51,342 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:36:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:37:03,031 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:03] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:03,039 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:03,974 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:05,158 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:05] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:05,163 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:05,469 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:05] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:06,784 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:06] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:37:14,701 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:14] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:37:15,349 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:37:15,368 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:15,679 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:19,717 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:19] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:19,720 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:20,032 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:20] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:20,331 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:20] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:20,339 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:21,756 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:21] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:21,757 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:31,801 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:31] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:32,116 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:32] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:33,278 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:33,581 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:41,315 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:41] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:41,625 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:37:43,177 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:37:43,482 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:43] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:43,794 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:43] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:37:47,986 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:37:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:39:08,365 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:08] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-14 10:39:09,131 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:09] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 10:39:09,444 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:09] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 10:39:09,797 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:09] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-14 10:39:10,019 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:10] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:39:23,816 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:23] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:39:23,885 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:23] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:39:24,266 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:39:24] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:46:08,431 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:46:08] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:46:08,744 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:46:08] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:18,731 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:18] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:19,089 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:19] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-14 10:51:19,313 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:19] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:23,491 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:23] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:51:23,496 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:51:23,808 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:51:24,049 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:51:25,728 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:25] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:51:31,057 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:31] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:31,065 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:35,033 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:51:38,551 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:38] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:51:38,557 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:51:38,560 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:38] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:51:53,089 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:53] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:53,402 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:53] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:51:58,345 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:58] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:51:58,352 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:51:58,656 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:51:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:52:01,030 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:05,284 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:05] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:05,602 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:05] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:09,128 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:09] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:52:22,050 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:22] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:52:22,381 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:22,624 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:24,965 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:24] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:24,970 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:24,971 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:28,113 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:28] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:52:28,121 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:28] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:52:32,765 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:32] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:52:32,767 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:34,846 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:35,145 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:35,457 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:49,166 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:49] "OPTIONS /api/courses/map-student HTTP/1.1" 200 -
2025-05-14 10:52:49,196 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:49] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-14 10:52:53,990 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:57,782 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:52:58,082 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:58] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:58,399 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:52:59,767 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:59] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:52:59,772 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:52:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:02,499 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:02,500 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:02] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:04,631 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:35,785 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:53:35,798 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:53:36,104 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:36] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:36,951 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:36] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:36,956 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:36] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:38,470 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:38,775 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:38] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:39,852 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:41,857 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:42,155 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:42] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:53:42,469 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:42] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:53:43,379 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:43] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:43,386 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:53:44,349 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:44,938 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:44] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:53:44,940 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:56,892 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:56] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:53:57,220 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:53:57,484 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:53:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:06,981 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:06] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:54:07,314 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:07] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:07,315 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:07] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:07,322 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:07] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:07,641 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:07] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:07,955 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:07] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:08,269 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:11,286 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:11] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:11,603 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:11] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:11,606 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:11,875 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:11,919 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:12,183 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:15,853 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:15] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:15,854 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:15] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:15,861 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:16,184 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:16,514 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:16] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:16,524 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:16,820 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:16] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:16,841 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:17,100 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:17,151 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:18,495 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:18] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:18,496 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:18] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:18,503 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:18,814 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:19,134 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:19,313 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:19,446 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:19,630 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:19,634 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:19,791 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:19,947 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:20,692 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:20] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:20,694 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:20] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:20,706 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:21,020 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:21,331 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:21,644 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:21,963 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:21] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:22,271 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:22] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:22,280 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:22,550 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:22,598 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:22,862 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:30,221 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:54:30,543 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:30,546 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:30,580 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:30,585 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:30,886 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:30,896 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:30] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:31,155 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:31] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:38,646 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:38] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:38,962 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:38] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:38,967 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:39,231 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:39,275 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:39] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:39,282 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:39] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:39,537 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:39] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:39,852 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:39] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:41,164 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:41] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:41,165 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:41] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:41,176 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:41,185 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:41] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:41,500 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:41,820 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:41] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:42,771 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:42] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:43,083 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:43] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:43,085 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:43,094 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:43] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:43,348 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:43,691 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:43] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:44,218 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:44,221 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 10:54:44,226 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:44,235 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:44,545 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:54:44,550 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:54:44,889 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:54:44] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 10:55:16,798 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:16] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:55:17,138 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:55:17,388 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:55:19,693 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:55:20,006 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:20] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:55:20,322 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:55:21,321 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:55:31,871 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:55:33,454 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:33] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:55:33,459 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:55:33,762 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:55:42,618 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:42] "OPTIONS /api/students/map-parent HTTP/1.1" 200 -
2025-05-14 10:55:45,009 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:45] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-14 10:55:45,010 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-14 10:55:45,011 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:45] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-14 10:55:51,730 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:51] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:55:51,735 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:55:51] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:56:49,500 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:56:49] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 10:56:49,507 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:56:49] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:56:49,825 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:56:49] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-14 10:56:50,082 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:56:50] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:56:53,348 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:56:53] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 10:56:53,653 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:56:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 10:57:04,264 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:04] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-14 10:57:04,265 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-14 10:57:04,267 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:04] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-14 10:57:15,114 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 10:57:15,446 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:15] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 10:57:15,447 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:15] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 10:57:15,757 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:15,758 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:15] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:16,020 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:16,020 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:16] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:16,068 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:16,068 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:16] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:17,693 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:17,694 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:17] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:18,008 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:18,008 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:18] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:18,315 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:18,315 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:18] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:18,836 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:18,837 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:18] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:19,149 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:19,150 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:19] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 10:57:19,462 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:19,462 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:57:19] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:12:58,467 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:12:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:12:58,808 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:12:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:12:59,061 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:12:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:01,761 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:01] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:13:02,072 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:02] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:13:03,332 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:13:03,332 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:04,641 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:04] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:13:04,955 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:13:05,255 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:05] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:13:05,561 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:08,184 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:20,433 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:20] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:13:27,491 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:27] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:13:27,805 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:27] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:13:28,691 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:31,377 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:31] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:13:31,381 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:31,691 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:13:43,601 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:43] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:13:43,930 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:43] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:13:43,932 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:43] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:13:43,932 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:13:43,933 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:43] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:13:44,241 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:13:44,241 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:44] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:13:44,552 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:13:44,552 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:44] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:13:50,991 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:50] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 11:13:52,418 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:13:52,752 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:53,018 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:55,302 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:55] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:13:55,307 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:13:55,307 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:13:55] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:14:06,424 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:14:06] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-14 11:14:06,425 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-14 11:14:06,427 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:14:06] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-14 11:14:10,485 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:14:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 11:14:19,735 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:14:19] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 11:14:21,329 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:14:21] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 11:14:51,673 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:14:51] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 11:33:13,079 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:33:14,159 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:14] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:14,473 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:14] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:14,635 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:14] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:33:14,738 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:14] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:16,089 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:16] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:33,706 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:33] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:34,078 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:34] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-14 11:33:34,301 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:34] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:35,556 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:33:35,912 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:33:37,728 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:37] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:33:38,069 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:38] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:33:40,338 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:33:40,342 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:33:41,651 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:33:42,230 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:33:42,984 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:33:51,437 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:51] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-14 11:33:51,662 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-14 11:33:51,666 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:33:51] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-14 11:34:37,259 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:34:37,582 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:37] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:34:37,583 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:37] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:34:37,890 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:37,890 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:37] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:34:38,147 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:38,147 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:38] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:34:38,196 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:38,197 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:38] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:34:49,216 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:34:49,254 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:49] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:34:49,257 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:49,257 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:49] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:34:49,571 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:49] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:34:49,879 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:49,879 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:34:49] "[31m[1mGET /api/parents/parents/user/21 HTTP/1.1[0m" 403 -
2025-05-14 11:35:45,655 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:35:45] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:35:45,993 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:35:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:35:46,257 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:35:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:35:46,892 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:35:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:35:47,191 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:35:47] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:35:47,506 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:35:47] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:36:11,199 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:36:11] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-14 11:36:11,201 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-14 11:36:11,202 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:36:11] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-14 11:40:38,280 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:40:38] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 11:40:38,281 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:40:38] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 11:40:38,600 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:40:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:40:38,859 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:40:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:49:46,984 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 11:49:46,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 11:49:46,984 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 11:49:46,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 11:49:47,137 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 11:49:47,137 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 11:49:47,142 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 11:49:47,142 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 11:49:47,266 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 11:49:47,266 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 11:50:18,995 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:18] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:50:19,715 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:19] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:50:20,064 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:50:20,317 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:50:22,535 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:22] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:50:22,862 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:22] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:50:24,059 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:24] "GET /api/courses/courses HTTP/1.1" 200 -

2025-05-14 11:50:25,415 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:25] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:50:25,733 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:25] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:50:27,095 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:27] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:50:27,411 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:27] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:50:37,188 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:37] "GET /api/parents/parents/2 HTTP/1.1" 200 -
2025-05-14 11:50:37,206 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:37] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-14 11:50:44,486 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:50:55,453 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:55] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:50:55,475 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:55] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:50:55,789 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:55] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:50:55,793 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:55] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:50:56,040 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:56] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:50:56,102 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:56] "OPTIONS /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 11:50:56,110 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:56] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 11:50:56,351 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:56] "OPTIONS /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 11:50:56,662 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:50:56] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 11:51:10,143 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:10] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 11:51:10,472 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:10] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 11:51:10,718 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:10] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 11:51:11,321 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:11] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 11:51:58,868 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:51:59,191 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:59] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:51:59,443 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:59] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 11:51:59,505 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:59] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 11:51:59,755 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:51:59] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 11:52:06,530 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:06] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 11:52:06,559 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:52:06,874 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:52:08,796 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:08] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:52:09,115 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:09] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:52:09,378 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:09] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 11:52:09,547 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:52:11,569 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:11] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 11:52:44,943 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:44] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-14 11:52:45,316 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:52:45] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-14 11:54:13,184 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:54:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:54:13,448 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 11:54:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 11:55:36,313 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 11:55:36,314 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 11:56:01,112 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 11:56:01,112 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:40:19,058 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 12:40:19,058 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:40:19,236 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 12:40:19,236 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:40:19,421 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 12:40:19,421 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:40:19,614 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 12:40:19,614 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:40:19,845 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 12:40:19,846 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:40:37,005 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 12:40:37,258 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 12:40:42,642 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:42] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 12:40:42,970 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:42] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 12:40:42,972 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:42] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 12:40:42,978 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 12:40:43,289 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 12:40:45,213 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 12:40:46,319 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:46] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 12:40:46,642 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:40:46] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 12:41:04,266 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:41:04] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 12:41:04,272 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:41:04] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 12:41:04,584 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:41:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 12:41:06,153 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:41:06] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 12:41:40,446 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:41:40] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-14 12:42:40,052 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:42:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 12:42:40,053 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:42:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 12:42:46,443 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:42:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 12:43:05,027 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:05] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-14 12:43:28,477 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-14 12:43:28,479 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:28] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-14 12:43:37,036 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 12:43:37,381 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 12:43:37,383 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 12:43:37,396 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:43:37,713 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:43:40,709 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:43:41,033 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:43:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:54:02,388 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 12:54:02,388 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:54:02,478 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 12:54:02,478 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:54:02,676 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 12:54:02,676 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:54:02,899 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 12:54:02,899 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:54:03,086 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 12:54:03,086 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:54:25,395 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:54:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:54:25,638 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:54:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:54:25,711 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:54:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:54:27,231 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:54:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:54:27,552 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:54:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:54:27,863 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 12:54:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 12:57:14,856 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 12:57:14,856 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:58:33,574 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 12:58:33,574 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 12:58:33,579 - werkzeug - INFO -  * Restarting with stat
2025-05-14 12:58:34,374 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 12:58:34,378 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 12:59:36,887 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 12:59:36,887 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:05:16,776 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:05:16,777 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:05:16,893 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:05:16,893 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:05:17,099 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:05:17,099 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:05:17,388 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:05:17,388 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:05:17,459 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:05:17,459 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:05:46,409 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:05:46] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:05:46,927 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:05:46] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:05:47,323 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:05:47] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:05:47,587 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:05:47] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:05:47,650 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:05:47] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:13:06,790 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:13:06,790 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:13:06,791 - werkzeug - INFO -  * Restarting with stat
2025-05-14 13:13:07,488 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 13:13:07,491 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 13:13:53,489 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:13:53] "GET /api/users/health HTTP/1.1" 200 -
2025-05-14 13:17:53,660 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:17:53,660 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:17:53,804 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:17:53,804 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:17:54,002 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:17:54,002 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:17:54,204 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:17:54,204 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:17:54,381 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:17:54,381 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:18:13,328 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:13] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:13,583 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:13] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:15,094 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:15,404 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:15,715 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:17,189 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:17] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:17,512 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:17] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:17,847 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:17] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:18,312 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:18] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:18,633 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:18] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:18,947 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:18] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:20,294 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:20] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:18:20,617 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:20] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:18:21,565 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:18:22,510 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:18:22,822 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:22] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:18:23,137 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:18:23,772 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:23] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:18:25,023 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:27,681 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:27] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:18:27,996 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:27] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:18:34,003 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:18:36,667 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:36] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:18:36,980 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:18:37,806 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:50,313 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:50] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:50,629 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:50] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:18:50,948 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:18:50] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:21:13,117 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:21:13,117 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:21:13,118 - werkzeug - INFO -  * Restarting with stat
2025-05-14 13:21:13,852 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 13:21:13,855 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 13:22:01,842 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:22:01] "GET /api/users/health HTTP/1.1" 200 -
2025-05-14 13:25:53,609 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:25:53,609 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:25:53,748 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:25:53,748 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:25:53,951 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:25:53,951 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:25:54,154 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:25:54,155 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:25:54,348 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:25:54,348 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:26:10,862 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:26:10,866 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:26:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:26:11,121 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:26:11,121 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:26:11] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:29:50,336 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:29:50,336 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:29:50,338 - werkzeug - INFO -  * Restarting with stat
2025-05-14 13:29:51,047 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 13:29:51,050 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 13:30:28,803 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:30:28,804 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:30:28] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:30:29,068 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:30:29,069 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:30:29] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:30:31,429 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:30:31,430 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:30:31] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:30:31,742 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:30:31,742 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:30:31] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:30:34,519 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:30:34] "GET /api/users/health HTTP/1.1" 200 -
2025-05-14 13:30:52,438 - common.middleware - WARNING - Invalid token for path: /api/users/users
2025-05-14 13:30:52,438 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:30:52] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-14 13:31:01,129 - common.middleware - WARNING - Invalid token for path: /api/users/users
2025-05-14 13:31:01,129 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:31:01] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-14 13:31:12,970 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:31:12] "GET /api/users/health HTTP/1.1" 200 -
2025-05-14 13:31:43,256 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:31:43,256 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:31:43] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:31:43,512 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:31:43,512 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:31:43] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:31:44,139 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:31:44,140 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:31:44] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:31:44,447 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:31:44,448 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:31:44] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:31:57,170 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:31:57,170 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:31:57,287 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:31:57,288 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:31:57,505 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:31:57,505 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:31:57,827 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:31:57,827 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:31:57,904 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:31:57,904 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:32:05,351 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:32:05,352 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:32:05] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:32:05,611 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:32:05,612 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:32:05] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:32:08,773 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:32:08] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:32:08,796 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:32:08,797 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:32:08] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:32:09,106 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:32:09,107 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:32:09] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:35:10,921 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:35:10,921 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:35:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:35:11,184 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:35:11,184 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:35:11] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:35:13,109 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:35:13,110 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:35:13] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:35:13,425 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:35:13,426 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:35:13] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:35:14,273 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:35:14,274 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:35:14] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:35:14,404 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:35:14,405 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:35:14] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:39:03,551 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:39:03,551 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:39:03,617 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:39:03,617 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:39:03,840 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:39:03,840 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:39:04,039 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:39:04,039 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:39:04,229 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:39:04,229 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:39:41,105 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:39:41,106 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:39:41,371 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:39:41,372 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:39:43,985 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:43] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:39:44,300 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:44] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:39:46,810 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:39:47,899 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:39:48,217 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:48] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:39:56,345 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:56] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:39:56,689 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:39:56,951 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:39:59,425 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:59] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:39:59,436 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:39:59] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:40:00,105 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:40:00,419 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:00] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:40:00,774 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:00] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:40:02,409 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:02] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:40:02,410 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:40:08,024 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:40:08,356 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 13:40:08,357 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 13:40:08,363 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 13:40:08,668 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 13:40:08,671 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "OPTIONS /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 13:40:08,680 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 13:40:08,934 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:08] "OPTIONS /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 13:40:09,246 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:09] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 13:40:17,133 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:40:17,155 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 13:40:17,167 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:40:17,473 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 13:40:17,473 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:40:17,480 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:40:17,787 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:40:18,101 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:18] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:40:27,140 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:27] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:40:27,163 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:40:27,164 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:40:27,482 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:40:27,482 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:40:31,086 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:31] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:40:31,091 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:40:32,625 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:40:33,618 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 13:40:33,620 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:40:35,761 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:40:35,762 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:40:35] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:03,523 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:03,524 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:03,847 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:03,847 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:14,843 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:14] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:41:15,178 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:15,179 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:15,445 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:15,446 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:18,273 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:18,273 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:18] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:18,589 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:18,590 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:18] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:27,041 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:41:27,375 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 13:41:27,377 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 13:41:27,638 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 13:41:27,642 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:41:27,698 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 13:41:27,948 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:27] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:28,012 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:41:28,263 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:28] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:28,520 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:28] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:45,098 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:45] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 13:41:45,099 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:45] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 13:41:45,424 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:41:45,674 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 13:41:45,734 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:45] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:45,984 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:45] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:46,049 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:46] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:46,317 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:46] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 13:41:55,046 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:55] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:41:55,391 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:55,392 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:55] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:41:55,656 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:41:55,657 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:41:55] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:42:29,260 - common.middleware - WARNING - Expired token for path: /api/users/users
2025-05-14 13:42:29,261 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:42:29] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-14 13:43:29,964 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:43:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 13:44:06,821 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:44:06,821 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:44:06] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:48:23,874 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:48:23,874 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:50:09,274 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 13:50:09,274 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:50:09] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:58:28,839 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:58:28] "GET /api/users/health HTTP/1.1" 200 -
2025-05-14 13:58:33,056 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-14 13:58:33,056 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:58:33] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-14 14:20:14,604 - user_service.common.utils - ERROR - Error: An error occurred while retrieving users: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?, Status: 500
2025-05-14 14:20:14,605 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:20:14] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 14:22:10,111 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 14:22:10,111 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 14:22:10,270 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 14:22:10,270 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 14:22:10,480 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 14:22:10,480 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 14:22:10,705 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 14:22:10,705 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 14:22:10,893 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 14:22:10,893 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 14:23:00,717 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:00] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 14:23:00,718 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:00] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 14:23:01,037 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:01] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:23:01,296 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:01] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:23:14,468 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:14] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:23:14,785 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:14] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:23:15,930 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:23:18,930 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:23:19,240 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:19] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:23:19,564 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:19] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:23:23,080 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:23] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:23:45,735 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:23:46,046 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:23:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:24:05,789 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:24:05] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 14:24:06,449 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:24:06] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 14:24:06,477 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:24:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:24:06,789 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:24:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:26:25,500 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:26:25] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 14:26:25,832 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:26:25] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:26:26,096 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:26:26] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:27:35,720 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:27:36,043 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:27:38,844 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:27:40,874 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:27:41,184 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:27:42,409 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:42] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:27:52,875 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:52] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:27:52,882 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:52] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:27:56,005 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:27:58,731 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:27:58,731 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:27:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:28:01,516 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:01] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:04,115 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:04] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:28:04,423 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:04] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:28:06,878 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:07,935 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:07] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:20,593 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:20,919 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:22,151 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:22] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:22,412 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:22] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:28,666 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:28] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:28:28,672 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:28] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:28:29,550 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:30,683 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:30] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:28:30,684 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:31,465 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:31] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:33,798 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:33] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:34,035 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:34] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:41,565 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:28:41,566 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:43,146 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:44,049 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:44] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:28:45,247 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:45] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:28:45,561 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:45] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:28:45,791 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:47,649 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:47] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:28:47,651 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:28:49,070 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:28:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:30:08,636 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:08] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:08,952 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:08] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:09,512 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:10,060 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:10] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:10,369 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:10] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:30:11,449 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:30:11,857 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:11] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:12,883 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:13,220 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:13] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:30:13,221 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:16,138 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:30:16,971 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:16] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:17,287 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:17] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:18,022 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:18,454 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:18,766 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:30:19,930 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:19] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:30:22,487 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:22] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:22,495 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:22] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:30:23,613 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:24,016 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:30:24,016 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:30:25,353 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:30:25] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:44:56,205 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:56] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:44:56,519 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:56] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:44:57,048 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:57] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 14:44:57,360 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:44:57,630 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:44:57,874 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:44:59,008 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:44:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:45:03,992 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:45:03] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:45:03,998 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:45:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:45:04,782 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:45:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:45:05,686 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:45:05] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:45:06,000 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:45:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:45:07,427 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:45:07] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:55:10,224 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:55:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 14:55:10,609 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:55:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:55:10,807 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:55:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 14:56:43,548 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:56:43] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:56:43,865 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:56:43] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:56:59,396 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:56:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:57:20,366 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:57:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:57:20,674 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:57:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:57:46,157 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:57:46] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 14:57:46,502 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:57:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:57:46,761 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:57:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:05,366 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:05] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:58:05,680 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:05] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:58:08,887 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:08] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:58:08,888 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:10,887 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:10] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:12,306 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:12,307 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:18,086 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:18,400 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:21,504 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:21,752 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:22,362 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:22,674 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:30,505 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 14:58:30,846 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:31,096 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:37,242 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:40,233 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:40,547 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:42,517 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:43,657 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:43,658 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:44,697 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:44] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:58:44,699 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:45,014 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:45] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 14:58:45,540 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 14:58:45,861 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:49,274 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:49,584 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:51,035 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:51,298 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:51,829 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 14:58:52,139 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 14:58:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:34:45,402 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 15:34:45,402 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 15:34:45,403 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 15:34:45,403 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 15:34:45,605 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 15:34:45,605 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 15:34:45,796 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 15:34:45,796 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 15:34:45,975 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 15:34:45,976 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 15:35:08,751 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:08] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 15:35:09,986 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:09] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 15:35:10,394 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:10] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 15:35:10,397 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:10] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 15:35:10,759 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 15:35:11,001 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 15:35:44,056 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:44] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:35:44,408 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:44] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:35:49,411 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:35:51,722 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:35:52,025 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:52] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 15:35:52,369 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:52] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 15:35:56,133 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:35:58,060 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:58] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:35:58,090 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:35:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:36:00,718 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:00] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 15:36:11,827 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:36:18,539 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:18] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:36:18,874 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:36:22,941 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:22] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 15:36:23,258 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:36:52,543 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:36:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:03,146 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:03] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 15:37:03,260 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:03,585 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:09,633 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:09] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:37:09,967 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:09] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:37:15,671 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:15] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:37:15,694 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:15] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:37:15,998 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:18,238 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 15:37:19,251 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:19,562 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:19] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 15:37:20,123 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 15:37:21,536 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 15:37:21,540 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:30,949 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 15:37:31,362 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:31] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 15:37:31,366 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:31] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-14 15:37:31,392 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:31,716 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:31] "OPTIONS /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 15:37:31,726 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 15:37:31,757 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:31] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 15:37:32,187 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:32] "GET /api/courses/student-courses/3 HTTP/1.1" 200 -
2025-05-14 15:37:54,833 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:54] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 15:37:55,184 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 15:37:55,187 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "OPTIONS /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 15:37:55,212 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 15:37:55,229 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "OPTIONS /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 15:37:55,535 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 15:37:55,572 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 15:37:55,826 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:37:55] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 15:39:14,353 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:39:14] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 15:39:14,608 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:39:14] "GET /api/parents/parents/user/21 HTTP/1.1" 200 -
2025-05-14 15:39:14,681 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:39:14] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 15:39:14,942 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 15:39:14] "GET /api/students/parent-students/2 HTTP/1.1" 200 -
2025-05-14 16:18:22,813 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 16:18:22,813 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:18:22,917 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 16:18:22,917 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:18:23,108 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 16:18:23,108 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:18:23,326 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 16:18:23,326 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:18:23,560 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 16:18:23,560 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:18:46,323 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 16:18:46] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-14 16:19:07,029 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 16:19:07] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-14 16:24:04,303 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 16:24:04,303 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:24:26,835 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 16:24:26] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-14 16:57:49,079 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 16:57:49,079 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:57:49,253 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 16:57:49,253 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:57:49,458 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 16:57:49,458 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:57:49,673 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 16:57:49,673 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:57:49,892 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 16:57:49,892 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 16:58:25,845 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 16:58:25] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 16:58:26,362 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-14 16:58:26,394 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 16:58:26] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-14 16:59:31,869 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-14 16:59:31,870 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 16:59:31] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-14 17:03:09,200 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 17:03:09,200 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:03:09,321 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 17:03:09,321 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:03:09,508 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 17:03:09,508 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:03:09,746 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 17:03:09,746 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:03:09,959 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 17:03:09,959 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:03:32,565 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:03:32] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:03:33,266 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:03:33] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:04:56,951 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:04:56] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:05:47,223 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:05:47] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:11:55,679 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 17:11:55,679 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:14:15,682 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 17:14:15,682 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:15:26,414 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:26] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:15:26,935 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:26] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:15:27,331 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:27] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 17:15:27,333 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:27] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 17:15:27,651 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:27] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:15:27,909 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:27] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:15:45,761 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:15:45] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:16:25,800 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:16:25] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-14 17:16:26,326 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:16:26] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 17:16:26,374 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:16:26] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:24:53,734 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 17:24:53,734 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:25:35,699 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:25:35] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:25:51,404 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:25:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:25:51,725 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:25:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:26:11,697 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:26:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:26:25,382 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:26:25] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:26:37,676 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:26:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:26:50,387 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:26:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:27:06,700 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:27:06] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:27:20,367 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:27:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:27:59,711 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:27:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:27:59,980 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:27:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:28:24,575 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:28:24] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:28:24,895 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:28:24] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:28:26,331 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:28:26] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:28:26,613 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:28:26] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:28:44,225 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:28:44] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 17:28:44,568 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:28:44] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:31:24,996 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:31:24] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:31:25,088 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:31:25] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:31:37,397 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:31:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:32:16,725 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:32:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:32:30,415 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:32:30] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:32:44,727 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:32:44] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:33:11,576 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:33:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:33:11,896 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:33:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:33:16,558 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:33:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:34:03,749 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:34:03] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:34:04,015 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:34:04] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:35:33,642 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:35:33] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:35:33,907 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:35:33] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:44:40,280 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:44:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:44:40,633 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:44:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:44:40,644 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:44:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:46:51,519 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:46:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:46:51,793 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:46:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:47:05,707 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:47:05] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:51:18,145 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 17:51:18,145 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:51:18,369 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 17:51:18,369 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:51:18,775 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 17:51:18,775 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:51:19,178 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 17:51:19,178 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:51:19,644 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 17:51:19,644 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 17:51:39,986 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:51:40,418 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:51:40,660 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 17:51:47,785 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:47] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 17:51:48,229 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:48] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 17:51:48,243 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:48] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 17:51:48,770 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:48] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:51:48,921 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:48] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:51:49,177 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:51:49] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:53:58,552 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:53:58] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:53:58,784 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:53:58] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:53:58,882 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:53:58] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:59:08,483 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:08] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 17:59:08,824 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:08] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 17:59:09,958 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:09] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 17:59:10,333 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:10] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:59:11,643 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:11] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 17:59:12,004 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:12] "[35m[1mGET /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 17:59:13,295 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:13] "[35m[1mGET /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 17:59:13,299 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:13] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:59:37,441 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:37] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:59:37,705 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:37] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:59:37,767 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:37] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 17:59:51,963 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:51] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 17:59:52,280 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:52] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 17:59:55,040 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 17:59:55] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:04:12,792 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 18:04:12,792 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:04:18,385 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 18:04:18,385 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:10:21,436 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 18:10:21,436 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:10:21,436 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 18:10:21,436 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:10:21,436 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:10:21,505 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 18:10:21,505 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:10:38,323 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:38] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 18:10:38,679 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:10:38,936 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:10:41,397 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:41] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 18:10:42,001 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:42] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:10:43,673 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:43] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:10:46,783 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:46] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:10:47,101 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:47] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:10:48,201 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:48] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:10:49,760 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:49] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:10:49,815 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:49] "[35m[1mGET /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 18:10:51,251 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:10:51,823 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:51] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:10:58,461 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 18:10:58,511 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:58] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:10:58,839 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:58] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:10:59,167 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:10:59] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:21:50,586 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:21:50] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:21:50,877 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:21:50] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:21:50,929 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:21:50] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:24:11,346 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:24:11] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-14 18:24:12,032 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:24:12] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 18:24:12,358 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:24:12] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 18:24:12,359 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:24:12] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 18:24:12,735 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:24:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:24:12,940 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:24:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:25:05,457 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:05] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:25:07,464 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:07] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:25:09,369 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:09] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:25:09,683 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:09] "[35m[1mGET /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 18:25:10,856 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:10] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:25:29,379 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:29] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:25:32,866 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:32] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:25:35,003 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:35] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:25:36,577 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:36] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:25:40,355 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:40] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:25:40,671 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:25:40] "[35m[1mGET /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 18:26:43,366 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:26:43] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:32:05,811 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:32:05] "[35m[1mGET /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:32:08,448 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:32:08] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:32:11,359 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:32:11] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:32:11,683 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:32:11] "[35m[1mGET /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 18:32:12,932 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:32:12] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:33:49,200 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:33:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:33:49,386 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:33:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:34:23,928 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:34:23] "[35m[1mGET /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:35:10,021 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:35:10] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-14 18:35:10,784 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:35:10] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 18:35:11,097 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:35:11] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-14 18:35:11,456 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:35:11] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:36:51,936 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:36:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:44:16,458 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 18:44:16,458 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:44:16,489 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:44:17,432 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:44:17,435 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:44:18,734 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 18:44:18,734 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:44:18,735 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:44:19,525 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:44:19,528 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:44:21,583 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 18:44:21,583 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:44:21,585 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:44:22,345 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:44:22,349 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:44:25,357 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 18:44:25,357 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:44:25,360 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:44:26,159 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:44:26,162 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:44:28,687 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 18:44:28,687 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:44:28,688 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:44:29,604 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:44:29,608 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:44:49,349 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:44:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 18:45:10,327 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:45:10] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 18:45:18,714 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:45:18] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 18:45:26,724 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:45:26] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:46:18,396 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 18:46:18,396 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:46:18,398 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:46:19,243 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:46:19,246 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:46:25,289 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:46:25] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:46:55,223 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:46:55,361 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:46:55,367 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:46:55,371 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:46:55,525 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:46:55,559 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:46:55,723 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:46:55,796 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:46:55,969 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:46:56,034 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:46:56,364 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:46:56,368 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:46:56,517 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:46:56,521 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:46:56,531 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:46:56,535 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:46:56,858 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:46:56,861 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:46:57,006 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:46:57,009 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:05,122 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:05,267 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:05,541 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:05,643 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:05,655 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:05,695 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:05,817 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:05,818 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:05,994 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:06,166 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:06,292 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:06,296 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:06,705 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:06,709 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:06,804 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:06,808 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:06,818 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:06,822 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:07,120 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:07,124 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:20,912 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:47:20,997 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:47:20,998 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:47:21,042 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:21,139 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:21,150 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:21,302 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:47:21,436 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:21,484 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:47:21,634 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:22,036 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:22,039 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:22,086 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:22,089 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:22,186 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:22,189 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:22,352 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:22,354 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:22,499 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:22,501 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:31,560 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 18:47:31,560 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:47:31,562 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:32,343 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:47:32,347 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:47:47,496 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:47:47] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:47:59,522 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:59,526 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:59,624 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:59,651 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:59,652 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:47:59,668 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:59,760 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:59,816 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:47:59,898 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\controllers.py', reloading
2025-05-14 18:48:00,055 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:00,651 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:00,655 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:00,657 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:00,662 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:00,762 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:00,765 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:00,770 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:00,773 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:00,955 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:00,958 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:21,131 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 18:48:21,131 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:48:21,132 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:22,011 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:22,014 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:31,345 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:48:31] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 500 -
2025-05-14 18:48:44,194 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:44,197 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:44,247 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:44,249 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:44,344 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:44,345 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:44,396 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:44,406 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:44,435 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:44,580 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:45,360 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:45,364 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:45,370 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:45,373 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:45,408 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:45,410 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:45,411 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:45,415 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:45,567 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:48:45,570 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:48:59,551 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:59,559 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:59,620 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:59,621 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:59,686 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:59,708 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:59,733 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:48:59,763 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:59,790 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:48:59,889 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:00,679 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:00,683 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:00,687 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:00,691 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:00,721 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:00,724 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:00,727 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:00,730 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:00,802 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:00,805 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:13,877 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:49:13,906 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:49:13,910 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:49:13,990 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\student_service\\models.py', reloading
2025-05-14 18:49:14,069 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:14,093 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:14,109 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:14,115 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:14,208 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:15,166 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:15,171 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:15,175 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:15,181 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:15,194 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:15,195 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:15,199 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:15,200 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:15,258 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:15,261 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:26,016 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 18:49:26,016 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:49:26,018 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:49:26,898 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:49:26,900 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:49:35,185 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:49:35] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-14 18:49:44,237 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:49:44] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 18:49:52,678 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:49:52] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 500 -
2025-05-14 18:50:10,877 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:10,912 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:10,913 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:10,929 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:11,108 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:11,119 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:11,129 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:11,142 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:11,402 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:11,591 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:12,082 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:12,086 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:12,093 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:12,096 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:12,097 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:12,099 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:12,104 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:12,110 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:12,506 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:12,510 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:25,676 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:25,785 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:26,282 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:26,331 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:26,334 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:26,339 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:26,468 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:26,536 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:26,536 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:26,539 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:26,773 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:26,777 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:27,407 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:27,410 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:27,457 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:27,460 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:27,462 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:27,464 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:27,465 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:27,467 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:37,534 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:37,601 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:37,624 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:37,629 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:37,635 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:37,751 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:37,764 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:37,769 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:37,921 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\models.py', reloading
2025-05-14 18:50:38,060 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:50:38,553 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:38,556 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:38,637 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:38,640 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:38,642 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:38,645 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:38,661 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:38,664 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:38,922 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:50:38,925 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:50:59,808 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\controllers.py', reloading
2025-05-14 18:50:59,879 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\controllers.py', reloading
2025-05-14 18:50:59,882 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\controllers.py', reloading
2025-05-14 18:50:59,887 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\controllers.py', reloading
2025-05-14 18:50:59,922 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:51:00,017 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:51:00,030 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:51:00,031 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:51:00,171 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\parent_service\\controllers.py', reloading
2025-05-14 18:51:00,320 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:51:00,956 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:51:00,960 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:51:00,994 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:51:00,996 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:51:00,998 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:51:00,999 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:51:01,000 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:51:01,002 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:51:01,241 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:51:01,245 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:51:21,335 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 18:51:21,335 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:51:21,338 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:51:22,171 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:51:22,174 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:54:30,717 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:54:30] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-14 18:54:42,501 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:54:42] "GET /api/parents/parents/3 HTTP/1.1" 200 -
2025-05-14 18:54:42,516 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:54:42] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-14 18:54:51,746 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:54:51] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-14 18:55:00,072 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:55:00] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 500 -
2025-05-14 18:55:21,222 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:21,234 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:21,268 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:21,301 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:21,309 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:21,377 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:21,457 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:21,497 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:21,559 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:21,568 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:22,537 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:22,543 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:22,548 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:22,553 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:22,560 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:22,564 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:22,612 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:22,615 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:22,679 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:22,683 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:37,746 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:37,747 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:37,759 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:37,807 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:37,857 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:37,889 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:37,899 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:37,912 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:37,955 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:38,007 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:38,862 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:38,866 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:38,873 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:38,877 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:38,886 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:38,889 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:38,890 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:38,892 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:38,936 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:38,939 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:51,024 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:51,026 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:51,063 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:51,068 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:51,076 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\models.py', reloading
2025-05-14 18:55:51,178 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:51,192 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:51,219 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:51,225 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:51,245 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:55:52,157 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:52,160 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:52,163 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:52,166 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:52,167 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:52,172 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:52,178 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:55:52,315 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:55:52,318 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:06,337 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\controllers.py', reloading
2025-05-14 18:56:06,352 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\controllers.py', reloading
2025-05-14 18:56:06,370 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\controllers.py', reloading
2025-05-14 18:56:06,373 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\controllers.py', reloading
2025-05-14 18:56:06,483 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:56:06,491 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\pandor2-fl\\course_service\\controllers.py', reloading
2025-05-14 18:56:06,507 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:56:06,518 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:56:06,526 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:56:06,666 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:56:07,508 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:56:07,512 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:56:07,513 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:07,516 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:07,520 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:56:07,526 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:07,546 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:56:07,549 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:07,658 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:56:07,661 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:18,697 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 18:56:18,697 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 18:56:18,698 - werkzeug - INFO -  * Restarting with stat
2025-05-14 18:56:19,529 - werkzeug - WARNING -  * Debugger is active!
2025-05-14 18:56:19,532 - werkzeug - INFO -  * Debugger PIN: 635-************-05-14 18:56:27,266 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:56:27] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-14 18:56:37,306 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:56:37] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-14 18:56:49,503 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:56:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 18:57:04,361 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:57:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 18:57:18,164 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:57:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 18:57:26,120 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:57:26] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 18:57:35,206 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:57:35] "[31m[1mGET /api/students/student-parents/4 HTTP/1.1[0m" 405 -
2025-05-14 18:59:51,538 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:59:51] "[31m[1mGET /api/students/student-parents HTTP/1.1[0m" 405 -
2025-05-14 18:59:59,955 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 18:59:59] "[31m[1mGET /api/courses/course-students/6 HTTP/1.1[0m" 405 -
2025-05-14 19:02:05,301 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:05] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 19:02:05,503 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:05] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 19:02:20,886 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:20] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-14 19:02:20,915 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 19:02:21,231 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:21] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 19:02:23,791 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:23] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 19:02:31,112 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:31] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-14 19:02:31,119 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 19:02:31,431 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 19:02:44,750 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:02:44] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 19:03:25,397 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:03:25] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 19:03:27,252 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:03:27] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 19:04:28,870 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:04:28] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-14 19:04:28,876 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:04:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 19:04:29,540 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:04:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-14 19:04:33,699 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:04:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 19:04:34,732 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 19:04:34] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 10:25:41,249 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-15 10:25:41,249 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:25:41,249 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:25:41,249 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
elopment server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-15 10:25:41,250 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:26:26,918 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:26] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-15 10:26:27,441 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:27] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 10:26:27,788 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:27] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-15 10:26:27,789 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:27] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-15 10:26:28,112 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:28] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 10:26:28,378 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:28] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 10:26:30,656 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:30] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:26:30,981 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:30] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:26:33,265 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:33] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-15 10:26:33,589 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:26:35,804 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:26:36,111 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:36] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:26:36,426 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:36] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:26:39,401 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:26:39] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 10:28:04,196 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:28:04] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-15 10:28:04,875 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:28:04] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:28:04,882 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:28:04] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 10:29:05,305 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:29:05] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:30:23,951 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:30:23] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-15 10:30:24,197 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:30:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:34:55,098 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:34:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:35:52,105 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:35:52] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:35:52,126 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:35:52] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-15 10:35:52,447 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:35:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:35:54,741 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:35:54] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:35:55,054 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:35:55] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:36:58,737 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:36:58] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:36:59,064 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:36:59] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-15 10:36:59,317 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:36:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:37:28,380 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:28] "OPTIONS /api/students/map-parent HTTP/1.1" 200 -
2025-05-15 10:37:30,743 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:30] "GET /api/parents/parents/4 HTTP/1.1" 200 -
2025-05-15 10:37:30,762 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:30] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-15 10:37:47,054 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:47] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:37:58,339 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 10:37:58,370 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:37:58,692 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:37:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:41:20,715 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:41:20] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:41:21,033 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:41:21] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-15 10:41:21,290 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:41:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:41:37,107 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:41:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:42:01,855 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:01] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-15 10:42:02,165 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:02] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:42:06,158 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:42:06,158 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:06] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:42:14,248 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:14] "OPTIONS /api/courses/map-student HTTP/1.1" 200 -
2025-05-15 10:42:14,574 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:14] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-15 10:42:24,631 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:24] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-15 10:42:27,074 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:27] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:42:59,558 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:59] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:42:59,564 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:59] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-15 10:42:59,885 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:42:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:43:02,832 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:43:02] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:43:03,147 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:43:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:43:17,976 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:43:17] "GET /api/parents/parents/5 HTTP/1.1" 200 -
2025-05-15 10:43:17,986 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:43:17] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-15 10:43:24,216 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:43:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:44:22,730 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:44:22] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:44:22,738 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:44:22] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-15 10:44:23,060 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:44:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:44:30,646 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:44:30] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 10:44:34,351 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:44:34] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:46:13,823 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-15 10:46:13,825 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:13] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-15 10:46:39,391 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:39] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 10:46:39,722 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:39] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-15 10:46:39,963 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:39] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:46:42,480 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 10:46:42,782 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:46:58,558 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:58] "GET /api/parents/parents/6 HTTP/1.1" 200 -
2025-05-15 10:46:58,567 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:46:58] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-15 10:56:52,849 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:53,100 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:53,931 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:54,244 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:54] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:55,560 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:55,822 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:56,466 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:56:56,783 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:56:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:57:00,605 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:57:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:57:00,852 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:57:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 10:58:57,841 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-15 10:58:57,842 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:58:58,029 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-15 10:58:58,030 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:58:58,110 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-15 10:58:58,110 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:58:58,306 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-15 10:58:58,306 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:58:58,463 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-15 10:58:58,463 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 10:59:08,818 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:59:08] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 10:59:09,164 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:59:09] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 10:59:09,404 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 10:59:09] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 11:00:13,346 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:00:13] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-15 11:00:13,352 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:00:13] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 11:00:21,952 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:00:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 11:00:22,299 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:00:22] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 11:00:22,545 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:00:22] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 11:01:53,137 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:01:53] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 11:01:55,825 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:01:55] "GET /api/users/users HTTP/1.1" 200 -
2025-05-15 11:01:58,046 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:01:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:06:09,788 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 11:06:09,788 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 11:06:09,789 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:06:09,789 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:06:09,789 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:06:09,789 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:06:09,793 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 11:06:09,793 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:06:28,408 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:06:28] "[33mOPTIONS /login HTTP/1.1[0m" 404 -

/**
 * Application Configuration
 *
 * This file centralizes all environment variables and configuration settings.
 * It provides a single source of truth for application configuration.
 */

// API URLs
const API = {
  // Service-specific URLs (for backward compatibility)
  AUTH: import.meta.env.VITE_API_URL_AUTH,
  USERS: import.meta.env.VITE_API_URL_USERS,
  STUDENTS: import.meta.env.VITE_API_URL_STUDENTS,
  COURSES: import.meta.env.VITE_API_URL_COURSES,
  PARENTS: import.meta.env.VITE_API_URL_PARENTS,
};

// Environment settings
const ENV = {
  NAME: import.meta.env.VITE_ENV || 'development',
  IS_PROD: import.meta.env.VITE_ENV === 'production',
  IS_DEV: import.meta.env.VITE_ENV === 'development' || import.meta.env.VITE_ENV === 'local',
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'info',
};

// Authentication settings
const AUTH = {
  TOKEN_KEY: 'token',
  USER_KEY: 'user',
  SCHOOL_CODE_KEY: 'main_code',
};

// Application settings
const APP = {
  NAME: 'School Management System',
  VERSION: '1.0.0',
};

// Export all configuration
const config = {
  API,
  ENV,
  AUTH,
  APP,
};

export default config;
